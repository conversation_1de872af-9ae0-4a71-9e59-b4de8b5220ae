import React, { useState, useRef, useEffect } from "react";
import { <PERSON><PERSON><PERSON>D<PERSON>, Star } from "lucide-react";
import { useQuery, useMutation, useAction } from "convex/react";
import { api } from "../../convex/_generated/api";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";

const PROVIDER_CONFIGS = {
  openai: {
    name: "OpenAI",
    models: [
      "gpt-4o-mini-2024-07-18",
      "chatgpt-4o-latest", 
      "o3-mini",
      "o4-mini",
      "gpt-4.1",
      "gpt-4.1-mini",
      "gpt-4.1-nano",
      "gpt-4o",
      "gpt-4o-mini",
      "gpt-4-turbo",
      "gpt-3.5-turbo"
    ],
    icon: "🤖",
  },
  google: {
    name: "Google AI",
    models: [
      "gemini-2.0-flash",
      "gemini-2.0-flash-lite", 
      "gemini-2.5-pro-preview-05-06",
      "gemini-2.5-flash-preview-05-20",
      "gemini-1.5-pro",
      "gemini-1.5-flash"
    ],
    icon: "🔍",
  },
  anthropic: {
    name: "Anthropic",
    models: [
      "claude-sonnet-4-20250514",
      "claude-opus-4-20250514",
      "claude-3-7-sonnet-latest",
      "claude-3-5-sonnet-latest",
      "claude-3-5-haiku-latest",
      "claude-3-5-sonnet-20241022",
      "claude-3-haiku-20240307",
      "claude-3-sonnet-20240229",
      "claude-3-opus-20240229"
    ],
    icon: "🧠",
  },
  openrouter: {
    name: "OpenRouter",
    models: [
      "deepseek/deepseek-chat-v3-0324:free",
      "deepseek/deepseek-r1:free",
      "tngtech/deepseek-r1t-chimera:free",
      "deepseek/deepseek-prover-v2:free",
      "mistralai/devstral-small:free",
      "qwen/qwen2.5-vl-72b-instruct:free",
      "mistralai/mistral-small-3.1-24b-instruct:free",
      "google/gemma-3-27b-it:free",
      "rekaai/reka-flash-3:free",
      "google/gemini-2.5-pro-exp-03-25:free",
      "qwen/qwen3-235b-a22b:free",
      "qwen/qwen3-30b-a3b:free",
      "qwen/qwen3-32b:free",
      "nvidia/llama-3.1-nemotron-ultra-253b-v1:free",
      "anthropic/claude-3.5-sonnet",
      "openai/gpt-4o",
      "google/gemini-pro-1.5",
      "meta-llama/llama-3.1-405b-instruct",
      "mistralai/mixtral-8x7b-instruct",
      "cohere/command-r-plus",
    ],
    icon: "🔀",
  },
  groq: {
    name: "Groq",
    models: [
      "deepseek-r1-distill-llama-70b",
      "deepseek-r1-distill-qwen-32b",
      "llama-3.3-70b-versatile",
      "llama-3.2-90b-vision-preview",
      "llama3-70b-8192",
      "qwen-qwq-32b",
      "meta-llama/llama-4-scout-17b-16e-instruct",
      "meta-llama/llama-4-maverick-17b-128e-instruct",
      "compound-beta",
      "compound-beta-mini",
      "llama-3.1-405b-reasoning",
      "llama-3.1-70b-versatile",
      "llama-3.1-8b-instant",
      "mixtral-8x7b-32768",
      "gemma2-9b-it",
    ],
    icon: "⚡",
  },
  deepseek: {
    name: "DeepSeek",
    models: ["deepseek-chat", "deepseek-coder"],
    icon: "🔍",
  },
  grok: {
    name: "Grok",
    models: ["grok-beta", "grok-vision-beta"],
    icon: "🚀",
  },
  cohere: {
    name: "Cohere",
    models: ["command-r-plus", "command-r", "command"],
    icon: "🎯",
  },
  mistral: {
    name: "Mistral AI",
    models: [
      "accounts/fireworks/models/mistral-small-24b-instruct-2501",
      "mistral-large-latest",
      "mistral-medium-latest",
      "mistral-small-latest", 
      "codestral-latest",
    ],
    icon: "🌟",
  },
};

// Model name mappings for better display names
const MODEL_DISPLAY_NAMES: Record<string, string> = {
  // OpenAI
  "gpt-4o-mini-2024-07-18": "GPT-4o Mini",
  "chatgpt-4o-latest": "ChatGPT 4o",
  "o3-mini": "O3 Mini",
  "o4-mini": "O4 Mini",
  "gpt-4.1": "GPT 4.1",
  "gpt-4.1-mini": "GPT 4.1 Mini",
  "gpt-4.1-nano": "GPT 4.1 Nano",
  "gpt-4o": "GPT-4o",
  "gpt-4o-mini": "GPT-4o Mini",
  "gpt-4-turbo": "GPT-4 Turbo",
  "gpt-3.5-turbo": "GPT-3.5 Turbo",
  
  // Google
  "gemini-2.0-flash": "Gemini 2.0 Flash",
  "gemini-2.0-flash-lite": "Gemini 2.0 Flash Lite",
  "gemini-2.5-pro-preview-05-06": "Gemini 2.5 Pro",
  "gemini-2.5-flash-preview-05-20": "Gemini 2.5 Flash",
  "gemini-1.5-pro": "Gemini 1.5 Pro",
  "gemini-1.5-flash": "Gemini 1.5 Flash",
  
  // Anthropic
  "claude-sonnet-4-20250514": "Claude 4 Sonnet",
  "claude-opus-4-20250514": "Claude 4 Opus",
  "claude-3-7-sonnet-latest": "Claude 3.7 Sonnet",
  "claude-3-5-sonnet-latest": "Claude 3.5 Sonnet",
  "claude-3-5-haiku-latest": "Claude 3.5 Haiku",
  "claude-3-5-sonnet-20241022": "Claude 3.5 Sonnet",
  "claude-3-haiku-20240307": "Claude 3 Haiku",
  "claude-3-sonnet-20240229": "Claude 3 Sonnet",
  "claude-3-opus-20240229": "Claude 3 Opus",
  
  // OpenRouter
  "deepseek/deepseek-chat-v3-0324:free": "DeepSeek Chat v3",
  "deepseek/deepseek-r1:free": "DeepSeek R1",
  "tngtech/deepseek-r1t-chimera:free": "DeepSeek R1T Chimera",
  "deepseek/deepseek-prover-v2:free": "DeepSeek Prover v2",
  "mistralai/devstral-small:free": "Devstral Small",
  "qwen/qwen2.5-vl-72b-instruct:free": "Qwen 2.5 VL 72B",
  "mistralai/mistral-small-3.1-24b-instruct:free": "Mistral Small 3.1 24B",
  "google/gemma-3-27b-it:free": "Gemma 3 27B IT",
  "rekaai/reka-flash-3:free": "Reka Flash 3",
  "google/gemini-2.5-pro-exp-03-25:free": "Gemini 2.5 Pro Exp",
  "qwen/qwen3-235b-a22b:free": "Qwen 3 235B",
  "qwen/qwen3-30b-a3b:free": "Qwen 3 30B",
  "qwen/qwen3-32b:free": "Qwen 3 32B",
  "nvidia/llama-3.1-nemotron-ultra-253b-v1:free": "Llama 3.1 Nemotron Ultra",
  "anthropic/claude-3.5-sonnet": "Claude 3.5 Sonnet",
  "openai/gpt-4o": "GPT-4o (OpenRouter)",
  "google/gemini-pro-1.5": "Gemini 1.5 Pro (OpenRouter)",
  "meta-llama/llama-3.1-405b-instruct": "Llama 3.1 405B",
  "mistralai/mixtral-8x7b-instruct": "Mixtral 8x7B",
  "cohere/command-r-plus": "Command R+ (OpenRouter)",
  
  // Groq
  "deepseek-r1-distill-llama-70b": "DeepSeek R1 Distill Llama 70B",
  "deepseek-r1-distill-qwen-32b": "DeepSeek R1 Distill Qwen 32B",
  "llama-3.3-70b-versatile": "Llama 3.3 70B",
  "llama-3.2-90b-vision-preview": "Llama 3.2 90B Vision",
  "llama3-70b-8192": "Llama 3 70B",
  "qwen-qwq-32b": "Qwen QwQ 32B",
  "meta-llama/llama-4-scout-17b-16e-instruct": "Llama 4 Scout 17B",
  "meta-llama/llama-4-maverick-17b-128e-instruct": "Llama 4 Maverick 17B",
  "compound-beta": "Compound Beta",
  "compound-beta-mini": "Compound Beta Mini",
  "llama-3.1-405b-reasoning": "Llama 3.1 405B Reasoning",
  "llama-3.1-70b-versatile": "Llama 3.1 70B",
  "llama-3.1-8b-instant": "Llama 3.1 8B",
  "mixtral-8x7b-32768": "Mixtral 8x7B",
  "gemma2-9b-it": "Gemma 2 9B",
  
  // DeepSeek
  "deepseek-chat": "DeepSeek Chat",
  "deepseek-coder": "DeepSeek Coder",
  
  // Grok
  "grok-beta": "Grok Beta",
  "grok-vision-beta": "Grok Vision Beta",
  
  // Cohere
  "command-r-plus": "Command R+",
  "command-r": "Command R",
  "command": "Command",
  
  // Mistral
  "accounts/fireworks/models/mistral-small-24b-instruct-2501": "Mistral Small 24B",
  "mistral-large-latest": "Mistral Large",
  "mistral-medium-latest": "Mistral Medium", 
  "mistral-small-latest": "Mistral Small",
  "codestral-latest": "Codestral",
};

/**
 * Convert a raw model identifier (e.g. "deepseek/deepseek-chat-v3-0324:free")
 * into a human-readable display name (e.g. "DeepSeek Chat V3 0324").
 * 1. If the model id is explicitly mapped in MODEL_DISPLAY_NAMES, that mapping
 *    is returned.
 * 2. Otherwise, apply heuristic transformations:
 *    – Drop any provider path prefix before the last slash (/)
 *    – Drop any tag suffix after a colon (:)
 *    – Replace dashes/underscores with spaces
 *    – Collapse multiple spaces
 *    – Title-case each word, upper-casing common acronyms (e.g. GPT, LLM)
 */
const getModelDisplayName = (model: string): string => {
  // Use explicit mapping if available
  if (MODEL_DISPLAY_NAMES[model]) return MODEL_DISPLAY_NAMES[model];

  // 1. Remove any provider prefix (everything before the last "/")
  let name = model.substring(model.lastIndexOf("/") + 1);

  // 2. Remove any tag suffix (everything after the first ":")
  const colonIndex = name.indexOf(":");
  if (colonIndex !== -1) name = name.substring(0, colonIndex);

  // 3. Replace separators with spaces and trim excess spaces
  name = name.replace(/[_-]+/g, " ").replace(/\s+/g, " ").trim();

  // 4. Title-case words, while upper-casing common acronyms
  const acronyms = new Set(["gpt", "llama", "qwen", "gemini", "grok", "mpt", "mixtral", "command", "cl", "ai"]);
  name = name
    .split(" ")
    .map((word) => {
      const lower = word.toLowerCase();
      if (acronyms.has(lower)) return lower.toUpperCase();
      return word.charAt(0).toUpperCase() + word.slice(1);
    })
    .join(" ");

  return name;
};

interface ModelSelectorProps {
  selectedProvider: keyof typeof PROVIDER_CONFIGS;
  selectedModel: string;
  onModelSelect: (provider: keyof typeof PROVIDER_CONFIGS, model: string) => void;
  /** Current enabled tool ids */
  enabledTools?: string[];
  /** Callback called when tools are toggled in parent component */
  onToolsChange?: (tools: string[]) => void;
}

export function ModelSelector({ selectedProvider, selectedModel, onModelSelect }: ModelSelectorProps) {
  const [showModelSelector, setShowModelSelector] = useState(false);
  const [activeTab, setActiveTab] = useState<"favorites" | "all">("favorites");
  const [availableProviders, setAvailableProviders] = useState<string[]>([]);
  const modelSelectorRef = useRef<HTMLDivElement>(null);

  const preferences = useQuery(api.preferences.get);
  const userApiKeys = useQuery(api.apiKeys.list) || [];
  const toggleFavoriteModel = useMutation(api.preferences.toggleFavoriteModel);
  const getAvailableProviders = useAction(api.ai.getAvailableProviders);

  const favoriteModels = preferences?.favoriteModels || [];

  // Load available providers on mount
  useEffect(() => {
    const loadProviders = async () => {
      try {
        const providers = await getAvailableProviders();
        console.log("Available providers from backend:", providers);
        setAvailableProviders(providers);
      } catch (error) {
        console.error("Failed to load available providers:", error);
        // Fallback to basic logic
        const fallbackProviders = Object.keys(PROVIDER_CONFIGS).filter(provider => {
          if (provider === "openai" || provider === "google") return true;
          return userApiKeys.some(key => key.provider === provider && key.hasKey);
        });
        console.log("Using fallback providers:", fallbackProviders);
        setAvailableProviders(fallbackProviders);
      }
    };
    void loadProviders();
  }, [getAvailableProviders, userApiKeys]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modelSelectorRef.current && !modelSelectorRef.current.contains(event.target as Node)) {
        setShowModelSelector(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleModelSelectInternal = (provider: keyof typeof PROVIDER_CONFIGS, model: string) => {
    onModelSelect(provider, model);
    setShowModelSelector(false);
  };

  const handleToggleFavorite = (e: React.MouseEvent, provider: string, model: string) => {
    e.stopPropagation();
    void toggleFavoriteModel({ provider, model });
  };

  const isFavorite = (provider: string, model: string) => {
    return favoriteModels.some(fav => fav.provider === provider && fav.model === model);
  };

  const getFavoriteModels = () => {
    return favoriteModels.filter(fav => 
      availableProviders.includes(fav.provider) &&
      PROVIDER_CONFIGS[fav.provider as keyof typeof PROVIDER_CONFIGS]?.models.includes(fav.model)
    );
  };

  return (
    <div className="relative" ref={modelSelectorRef}>
      <Button
        variant="outline"
        onClick={() => setShowModelSelector(!showModelSelector)}
        className={cn(
          "flex items-center gap-1.5 px-2 py-1 h-7 text-xs transition-all font-normal border-border/50",
          "hover:border-border hover:bg-muted/50",
          showModelSelector && "bg-muted border-border"
        )}
      >
        <span className="text-xs">{PROVIDER_CONFIGS[selectedProvider].icon}</span>
        <span className="text-muted-foreground max-w-24 truncate text-xs">
          {getModelDisplayName(selectedModel)}
        </span>
        <ChevronDown size={12} className={cn(
          "transition-transform text-muted-foreground/70", 
          showModelSelector && 'rotate-180'
        )} />
      </Button>

      {showModelSelector && (
        <div className="absolute bottom-full mb-2 left-0 bg-popover border border-border rounded-md shadow-md z-50 min-w-72 max-h-72 overflow-hidden">
          {/* Tabs */}
          <div className="flex border-b border-border bg-muted/30">
            <button
              onClick={() => setActiveTab("favorites")}
              className={cn(
                "flex-1 px-3 py-2 text-xs font-medium transition-colors",
                activeTab === "favorites"
                  ? "bg-background text-foreground border-b border-primary"
                  : "text-muted-foreground hover:text-foreground"
              )}
            >
              <Star size={12} className="inline mr-1" />
              Favorites
            </button>
            <button
              onClick={() => setActiveTab("all")}
              className={cn(
                "flex-1 px-3 py-2 text-xs font-medium transition-colors",
                activeTab === "all"
                  ? "bg-background text-foreground border-b border-primary"
                  : "text-muted-foreground hover:text-foreground"
              )}
            >
              All Models
            </button>
          </div>

          <div className="overflow-y-auto max-h-56">
            {activeTab === "favorites" ? (
              <div>
                {getFavoriteModels().length > 0 ? (
                  getFavoriteModels().map(fav => (
                    <button
                      key={`${fav.provider}:${fav.model}`}
                      onClick={() => handleModelSelectInternal(fav.provider as keyof typeof PROVIDER_CONFIGS, fav.model)}
                      className={cn(
                        "w-full text-left px-3 py-2 hover:bg-muted/50 text-xs flex items-center justify-between transition-colors",
                        selectedProvider === fav.provider && selectedModel === fav.model
                          ? 'bg-accent text-accent-foreground'
                          : 'text-foreground'
                      )}
                    >
                      <div className="flex items-center gap-2 min-w-0">
                        <span className="text-xs">{PROVIDER_CONFIGS[fav.provider as keyof typeof PROVIDER_CONFIGS].icon}</span>
                        <span className="text-xs font-medium">{getModelDisplayName(fav.model)}</span>
                      </div>
                      <Star size={12} className="text-yellow-500 fill-current flex-shrink-0" />
                    </button>
                  ))
                ) : (
                  <div className="p-4 text-xs text-muted-foreground text-center">
                    No favorites yet. Star models to add them here.
                  </div>
                )}
              </div>
            ) : (
              <div>
                {availableProviders.map(provider => (
                  <div key={provider} className="border-b border-border/50 last:border-b-0">
                    <div className="px-3 py-1.5 bg-muted/20 font-medium text-xs text-muted-foreground flex items-center gap-2">
                      <span className="text-xs">{PROVIDER_CONFIGS[provider as keyof typeof PROVIDER_CONFIGS].icon}</span>
                      <span>{PROVIDER_CONFIGS[provider as keyof typeof PROVIDER_CONFIGS].name}</span>
                      {(provider === "openai" || provider === "google") && (
                        <span className="px-1.5 py-0.5 bg-primary/10 text-primary text-[10px] rounded-sm font-medium">Built-in</span>
                      )}
                    </div>
                    {PROVIDER_CONFIGS[provider as keyof typeof PROVIDER_CONFIGS].models.map(model => (
                      <button
                        key={model}
                        onClick={() => handleModelSelectInternal(provider as keyof typeof PROVIDER_CONFIGS, model)}
                        className={cn(
                          "w-full text-left px-3 py-2 hover:bg-muted/50 text-xs flex items-center justify-between transition-colors",
                          selectedProvider === provider && selectedModel === model
                            ? 'bg-accent text-accent-foreground'
                            : 'text-foreground'
                        )}
                      >
                        <span className="text-xs">{getModelDisplayName(model)}</span>
                        <button
                          onClick={(e) => handleToggleFavorite(e, provider, model)}
                          className="p-1 hover:bg-muted rounded flex-shrink-0"
                        >
                          <Star 
                            size={12} 
                            className={cn(
                              "transition-colors",
                              isFavorite(provider, model) 
                                ? "text-yellow-500 fill-current" 
                                : "text-muted-foreground hover:text-foreground"
                            )} 
                          />
                        </button>
                      </button>
                    ))}
                  </div>
                ))}
                
                {availableProviders.length <= 2 && (
                  <div className="p-3 text-xs text-muted-foreground text-center">
                    Add API keys in settings for more providers
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
