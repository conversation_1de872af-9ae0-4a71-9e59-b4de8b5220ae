import { useState, useEffect } from "react";
import { useQuery, useMutation, useAction } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { Sidebar } from "./Sidebar";
import { ChatArea } from "./ChatArea";
import { MessageInput } from "./MessageInput";
import { SettingsModal } from "./SettingsModal";
import { Menu, Settings, BotMessageSquare } from 'lucide-react';
import { SignOutButton } from "../SignOutButton";
import { ModeToggle } from "./ModeToggle";
import { Button } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface UserPreferences {
  aiProvider: "openai" | "anthropic" | "google" | "openrouter" | "groq" | "deepseek" | "grok" | "cohere" | "mistral";
  model: string;
  temperature: number;
  maxTokens: number;
  enabledTools?: string[];
  favoriteModels?: Array<{
    provider: string;
    model: string;
  }>;
}

export function ChatInterface() {
  const [currentConversationId, setCurrentConversationId] = useState<Id<"conversations"> | null>(null);
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [settingsOpen, setSettingsOpen] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  
  const conversations = useQuery(api.conversations.listWithMessageCounts) || [];
  const messages = useQuery(
    api.messages.list,
    currentConversationId ? { conversationId: currentConversationId } : "skip"
  ) || [];
  
  const createConversation = useMutation(api.conversations.create);
  const addMessage = useMutation(api.messages.add);
  const generateResponse = useAction(api.ai.generateResponse);
  const generateTitle = useAction(api.conversations.generateTitle);
  const preferences = useQuery(api.preferences.get);

  useEffect(() => {
    if (!currentConversationId && conversations.length > 0) {
      setCurrentConversationId(conversations[0]._id);
    }
  }, [conversations, currentConversationId]);

  const handleNewConversation = async () => {
    const title = `New Chat ${conversations.length + 1}`;
    const conversationId = await createConversation({ title });
    setCurrentConversationId(conversationId);
  };

  const handleSendMessage = async (
    content: string, 
    attachments?: any[], 
    selectedModel?: { provider: string; model: string },
    enabledTools?: string[]
  ) => {
    if (!content.trim() || isGenerating) return;

    let conversationId = currentConversationId;
    let isFirstMessage = false;
    
    if (!conversationId) {
      const title = content.slice(0, 30) + (content.length > 30 ? "..." : "");
      conversationId = await createConversation({ title });
      setCurrentConversationId(conversationId);
      isFirstMessage = true;
    } else if (messages.length === 0) {
      isFirstMessage = true;
    }

    await addMessage({
      conversationId,
      role: "user",
      content,
      attachments,
    });

    // Generate a better title after the first user message
    if (isFirstMessage) {
      try {
        await generateTitle({
          conversationId,
          firstUserMessage: content
        });
      } catch (error) {
        console.error("Failed to generate title:", error);
      }
    }

    setIsGenerating(true);
    try {
      const messageHistory = messages.map(msg => ({
        role: msg.role,
        content: msg.content,
      }));

      messageHistory.push({ role: "user" as const, content });
      
      const provider = selectedModel?.provider || preferences?.aiProvider;
      const model = selectedModel?.model || preferences?.model;
      const toolsToUse = enabledTools || preferences?.enabledTools || [];

      await generateResponse({
        conversationId,
        messages: messageHistory,
        provider: provider as any,
        model,
        temperature: preferences?.temperature,
        maxTokens: preferences?.maxTokens,
        enabledTools: toolsToUse,
      });
    } catch (error) {
      console.error("Failed to generate response:", error);
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="flex h-screen bg-background text-foreground">
      <TooltipProvider>
        <div className={`transition-all duration-300 ${sidebarOpen ? 'w-80' : 'w-0'} overflow-hidden`}>
          <Sidebar
            conversations={conversations || []}
            currentConversationId={currentConversationId}
            onSelectConversation={setCurrentConversationId}
            onNewConversation={() => void handleNewConversation()}
            isSidebarOpen={sidebarOpen}
            onToggleSidebar={() => setSidebarOpen(!sidebarOpen)}
          />
        </div>

        <div className="flex-1 flex flex-col">
          <header className="border-b border-border/50 px-8 py-6 flex items-center justify-between h-20 bg-background/80 backdrop-blur-sm">
            <div className="flex items-center gap-4">
              {!sidebarOpen && (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setSidebarOpen(!sidebarOpen)}
                  className="rounded-2xl h-12 w-12"
                >
                  <Menu size={22} />
                </Button>
              )}
              <div className="flex items-center gap-4">
                <div className="p-3 bg-gradient-to-br from-primary to-primary/70 rounded-2xl shadow-lg">
                  <BotMessageSquare size={28} className="text-primary-foreground" />
                </div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-foreground to-muted-foreground bg-clip-text text-transparent">
                  ErzenAI
                </h1>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <ModeToggle />
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon" onClick={() => setSettingsOpen(true)} className="rounded-2xl h-12 w-12">
                    <Settings size={22} />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Settings</p>
                </TooltipContent>
              </Tooltip>
              <SignOutButton />
            </div>
          </header>

          <div className="flex-1 min-h-0">
            <ChatArea
              messages={messages}
              isGenerating={isGenerating}
              conversationId={currentConversationId}
            />
          </div>

          <div className="border-t border-border/50 bg-background/95 backdrop-blur-sm">
            <div className="max-w-4xl mx-auto w-full p-4">
              <MessageInput
                onSendMessage={(...args) => void handleSendMessage(...args)}
                disabled={isGenerating}
              />
            </div>
          </div>
        </div>

        {settingsOpen && (
          <SettingsModal
            onClose={() => setSettingsOpen(false)}
            preferences={preferences || null}
          />
        )}
      </TooltipProvider>
    </div>
  );
}
