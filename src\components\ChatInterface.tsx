import { useState, useEffect } from "react";
import { useQuery, useMutation, useAction } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { Sidebar } from "./Sidebar";
import { ChatArea } from "./ChatArea";
import { MessageInput } from "./MessageInput";
import { SettingsModal } from "./SettingsModal";
import { Menu, Settings, BotMessageSquare } from 'lucide-react';
import { SignOutButton } from "../SignOutButton";
import { ModeToggle } from "./ModeToggle";
import { Button } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface UserPreferences {
  aiProvider: "openai" | "anthropic" | "google" | "openrouter" | "groq" | "deepseek" | "grok" | "cohere" | "mistral";
  model: string;
  temperature: number;
  maxTokens: number;
  enabledTools?: string[];
  favoriteModels?: Array<{
    provider: string;
    model: string;
  }>;
}

export function ChatInterface() {
  const [currentConversationId, setCurrentConversationId] = useState<Id<"conversations"> | null>(null);
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [settingsOpen, setSettingsOpen] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  
  const conversations = useQuery(api.conversations.listWithMessageCounts) || [];
  const messages = useQuery(
    api.messages.list,
    currentConversationId ? { conversationId: currentConversationId } : "skip"
  ) || [];
  
  const createConversation = useMutation(api.conversations.create);
  const addMessage = useMutation(api.messages.add);
  const generateResponse = useAction(api.ai.generateResponse);
  const generateTitle = useAction(api.conversations.generateTitle);
  const preferences = useQuery(api.preferences.get);

  useEffect(() => {
    if (!currentConversationId && conversations.length > 0) {
      setCurrentConversationId(conversations[0]._id);
    }
  }, [conversations, currentConversationId]);

  const handleNewConversation = async () => {
    const title = `New Chat ${conversations.length + 1}`;
    const conversationId = await createConversation({ title });
    setCurrentConversationId(conversationId);
  };

  const handleSendMessage = async (
    content: string, 
    attachments?: any[], 
    selectedModel?: { provider: string; model: string },
    enabledTools?: string[]
  ) => {
    if (!content.trim() || isGenerating) return;

    let conversationId = currentConversationId;
    let isFirstMessage = false;
    
    if (!conversationId) {
      const title = content.slice(0, 30) + (content.length > 30 ? "..." : "");
      conversationId = await createConversation({ title });
      setCurrentConversationId(conversationId);
      isFirstMessage = true;
    } else if (messages.length === 0) {
      isFirstMessage = true;
    }

    await addMessage({
      conversationId,
      role: "user",
      content,
      attachments,
    });

    // Generate a better title after the first user message
    if (isFirstMessage) {
      try {
        await generateTitle({
          conversationId,
          firstUserMessage: content
        });
      } catch (error) {
        console.error("Failed to generate title:", error);
      }
    }

    setIsGenerating(true);
    try {
      const messageHistory = messages.map(msg => ({
        role: msg.role,
        content: msg.content,
      }));

      messageHistory.push({ role: "user" as const, content });
      
      const provider = selectedModel?.provider || preferences?.aiProvider;
      const model = selectedModel?.model || preferences?.model;
      const toolsToUse = enabledTools || preferences?.enabledTools || [];

      await generateResponse({
        conversationId,
        messages: messageHistory,
        provider: provider as any,
        model,
        temperature: preferences?.temperature,
        maxTokens: preferences?.maxTokens,
        enabledTools: toolsToUse,
      });
    } catch (error) {
      console.error("Failed to generate response:", error);
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="flex h-screen bg-background text-foreground">
      <TooltipProvider>
        <div className={`transition-all duration-300 ${sidebarOpen ? 'w-80' : 'w-0'} overflow-hidden`}>
          <Sidebar
            conversations={conversations || []}
            currentConversationId={currentConversationId}
            onSelectConversation={setCurrentConversationId}
            onNewConversation={() => void handleNewConversation()}
            isSidebarOpen={sidebarOpen}
            onToggleSidebar={() => setSidebarOpen(!sidebarOpen)}
          />
        </div>

        <div className="flex-1 flex flex-col">
          <header className="border-b px-6 py-4 flex items-center justify-between h-16">
            <div className="flex items-center gap-2">
              {!sidebarOpen && (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setSidebarOpen(!sidebarOpen)}
                >
                  <Menu size={20} />
                </Button>
              )}
              <div className="flex items-center gap-2">
                <BotMessageSquare size={24} className="text-primary" />
                <h1 className="text-xl font-semibold">
                  ErzenAI
                </h1>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <ModeToggle />
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon" onClick={() => setSettingsOpen(true)}>
                    <Settings size={20} />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Settings</p>
                </TooltipContent>
              </Tooltip>
              <SignOutButton />
            </div>
          </header>

          <div className="flex-1 min-h-0">
            <ChatArea
              messages={messages}
              isGenerating={isGenerating}
              conversationId={currentConversationId}
            />
          </div>

          <div className="border-t bg-background/95 backdrop-blur-sm">
            <div className="max-w-4xl mx-auto w-full p-2">
              <MessageInput
                onSendMessage={(...args) => void handleSendMessage(...args)}
                disabled={isGenerating}
              />
            </div>
          </div>
        </div>

        {settingsOpen && (
          <SettingsModal
            onClose={() => setSettingsOpen(false)}
            preferences={preferences || null}
          />
        )}
      </TooltipProvider>
    </div>
  );
}
