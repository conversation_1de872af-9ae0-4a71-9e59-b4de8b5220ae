import * as React from "react"
import { <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> } from "lucide-react"
import { useTheme } from "next-themes"
import { useEffect, useState } from "react"

import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from "@/components/ui/dropdown-menu"

const themes = [
  { name: "Light", value: "light", icon: Sun },
  { name: "Dark", value: "dark", icon: Moon },
  { name: "System", value: "system", icon: Monitor },
]

const colorThemes = [
  { name: "Default", value: "dark", color: "bg-slate-900" },
  { name: "Blue", value: "theme-blue", color: "bg-blue-600" },
  { name: "Green", value: "theme-green", color: "bg-green-600" },
  { name: "Purple", value: "theme-purple", color: "bg-purple-600" },
  { name: "Orange", value: "theme-orange", color: "bg-orange-500" },
]

export function ModeToggle() {
  const { theme, setTheme } = useTheme()
  const [colorTheme, setColorTheme] = useState("dark")

  useEffect(() => {
    // Get the current color theme from document class
    const currentColorTheme = colorThemes.find(t =>
      document.documentElement.classList.contains(t.value)
    )?.value || "dark"
    setColorTheme(currentColorTheme)
  }, [])

  const handleColorThemeChange = (newColorTheme: string) => {
    // Remove all color theme classes
    colorThemes.forEach(t => {
      document.documentElement.classList.remove(t.value)
    })

    // Add the new color theme class
    if (newColorTheme !== "dark") {
      document.documentElement.classList.add(newColorTheme)
    }

    setColorTheme(newColorTheme)
    localStorage.setItem("color-theme", newColorTheme)
  }

  // Load saved color theme on mount
  useEffect(() => {
    const savedColorTheme = localStorage.getItem("color-theme") || "dark"
    handleColorThemeChange(savedColorTheme)
  }, [])

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="rounded-xl">
          <Sun className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
          <Moon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
          <span className="sr-only">Toggle theme</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel className="flex items-center gap-2">
          <Palette size={16} />
          Theme Settings
        </DropdownMenuLabel>
        <DropdownMenuSeparator />

        {themes.map((themeOption) => {
          const Icon = themeOption.icon
          return (
            <DropdownMenuItem
              key={themeOption.value}
              onClick={() => setTheme(themeOption.value)}
              className="flex items-center justify-between"
            >
              <div className="flex items-center gap-2">
                <Icon size={16} />
                {themeOption.name}
              </div>
              {theme === themeOption.value && <Check size={16} />}
            </DropdownMenuItem>
          )
        })}

        <DropdownMenuSeparator />
        <DropdownMenuLabel>Color Themes</DropdownMenuLabel>

        {colorThemes.map((colorThemeOption) => (
          <DropdownMenuItem
            key={colorThemeOption.value}
            onClick={() => handleColorThemeChange(colorThemeOption.value)}
            className="flex items-center justify-between"
          >
            <div className="flex items-center gap-2">
              <div className={`w-4 h-4 rounded-full ${colorThemeOption.color}`} />
              {colorThemeOption.name}
            </div>
            {colorTheme === colorThemeOption.value && <Check size={16} />}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}