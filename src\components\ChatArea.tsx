import { useEffect, useRef } from "react";
import { Id } from "../../convex/_generated/dataModel";
import { MessageBubble } from "./MessageBubble";
import { TypingIndicator } from "./TypingIndicator";
import { MessageCircle, BotMessageSquare } from 'lucide-react';

interface ChatAreaProps {
  messages: any[];
  isGenerating: boolean;
  conversationId: Id<"conversations"> | null;
}

export function ChatArea({ messages, isGenerating, conversationId }: ChatAreaProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages, isGenerating]);

  if (!conversationId) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto">
          <div className="mb-4">
            <BotMessageSquare size={48} className="mx-auto text-primary" />
          </div>
          <h2 className="text-2xl font-semibold mb-2">
            Welcome to ErzenAI
          </h2>
          <p className="text-muted-foreground mb-6">
            Start a conversation to see what I can do.
          </p>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-left">
            <div className="bg-muted/50 p-4 rounded-lg">
              <h3 className="font-semibold mb-1">Multi-Provider AI</h3>
              <p className="text-sm text-muted-foreground">Access top models from OpenAI, Anthropic, Google, and more.</p>
            </div>
            <div className="bg-muted/50 p-4 rounded-lg">
              <h3 className="font-semibold mb-1">Smart Tools</h3>
              <p className="text-sm text-muted-foreground">Use tools like web search, calculator, and more.</p>
            </div>
            <div className="bg-muted/50 p-4 rounded-lg">
              <h3 className="font-semibold mb-1">Multimodal</h3>
              <p className="text-sm text-muted-foreground">Analyze images and get rich, contextual answers.</p>
            </div>
             <div className="bg-muted/50 p-4 rounded-lg">
              <h3 className="font-semibold mb-1">Conversation History</h3>
              <p className="text-sm text-muted-foreground">Your chats are saved and synced across devices.</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full overflow-y-auto p-6 md:p-8 bg-gradient-to-b from-background/50 to-background">
      <div className="max-w-4xl mx-auto space-y-8">
        {messages.length === 0 && !isGenerating ? (
          <div className="text-center py-24">
            <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-primary/20 to-primary/10 rounded-3xl flex items-center justify-center">
              <MessageCircle size={32} className="text-primary" />
            </div>
            <h3 className="text-xl font-semibold mb-2">Start your conversation</h3>
            <p className="text-muted-foreground">Ask me anything, and I'll help you out!</p>
          </div>
        ) : (
          messages.map((message) => (
            <MessageBubble key={message._id} message={message} />
          ))
        )}

        {isGenerating && <TypingIndicator />}
        <div ref={messagesEndRef} />
      </div>
    </div>
  );
}
